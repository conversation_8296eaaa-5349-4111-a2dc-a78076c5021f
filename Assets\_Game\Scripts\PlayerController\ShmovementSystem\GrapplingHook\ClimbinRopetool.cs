using UnityEngine;

[RequireComponent(typeof(ClimbingRopeSystem))]
public class ClimbingRopeTool : MonoBehaviour
{
    [Header("Tool Settings")]
    [SerializeField] private KeyCode fireKey = KeyCode.Mouse0;
    [SerializeField] private KeyCode releaseKey = KeyCode.Mouse1;
    [SerializeField] private bool requireHoldToMaintain = false;
    
    [Header("Visual Feedback")]
    [SerializeField] private GameObject ropeCoilModel;
    [SerializeField] private Transform ropeAnchorPoint;
    
    private ClimbingRopeSystem ropeSystem;
    private ToolManager toolManager;
    private bool isToolActive = false;
    private bool isFireKeyHeld = false;
    
    private void Awake()
    {
        ropeSystem = GetComponent<ClimbingRopeSystem>();
        if (ropeSystem == null)
        {
            ropeSystem = gameObject.AddComponent<ClimbingRopeSystem>();
        }
        
        // Try to find tool manager
        toolManager = GetComponentInParent<ToolManager>();
        
        // Setup rope coil visual if not assigned
        if (ropeCoilModel == null)
        {
            CreateDefaultRopeVisual();
        }
    }
    
    private void CreateDefaultRopeVisual()
    {
        // Create a simple rope coil visual
        ropeCoilModel = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        ropeCoilModel.name = "RopeCoilVisual";
        ropeCoilModel.transform.SetParent(transform);
        ropeCoilModel.transform.localPosition = new Vector3(0.1f, -0.1f, 0.2f);
        ropeCoilModel.transform.localRotation = Quaternion.Euler(0, 0, 90);
        ropeCoilModel.transform.localScale = new Vector3(0.15f, 0.3f, 0.15f);
        
        // Remove collider
        Destroy(ropeCoilModel.GetComponent<Collider>());
        
        // Set brown rope color
        Renderer renderer = ropeCoilModel.GetComponent<Renderer>();
        renderer.material.color = new Color(0.4f, 0.3f, 0.2f);
    }
    
    private void OnEnable()
    {
        isToolActive = true;
        ropeSystem.SetPredictionEnabled(true);
        
        if (ropeCoilModel != null)
            ropeCoilModel.SetActive(!ropeSystem.IsRopeAttached);
    }
    
    private void OnDisable()
    {
        isToolActive = false;
        ropeSystem.SetPredictionEnabled(false);
        
        // Release rope if tool is switched
        if (ropeSystem.IsRopeAttached && !requireHoldToMaintain)
        {
            ropeSystem.ReleaseRope();
        }
    }
    
    private void Update()
    {
        if (!isToolActive) return;
        
        // Handle firing rope
        if (Input.GetKeyDown(fireKey))
        {
            isFireKeyHeld = true;
            if (!ropeSystem.IsRopeAttached)
            {
                ropeSystem.FireRope();
                UpdateRopeVisual();
            }
        }
        
        if (Input.GetKeyUp(fireKey))
        {
            isFireKeyHeld = false;
            if (requireHoldToMaintain && ropeSystem.IsRopeAttached)
            {
                ropeSystem.ReleaseRope();
                UpdateRopeVisual();
            }
        }
        
        // Handle releasing rope
        if (Input.GetKeyDown(releaseKey))
        {
            if (ropeSystem.IsRopeAttached)
            {
                ropeSystem.ReleaseRope();
                UpdateRopeVisual();
            }
        }
        
        // Update visual state
        UpdateRopeVisual();
    }
    
    private void UpdateRopeVisual()
    {
        if (ropeCoilModel != null)
        {
            // Hide rope coil when rope is deployed
            ropeCoilModel.SetActive(!ropeSystem.IsRopeAttached);
        }
    }
    
    // Public method for tool system integration
    public bool IsRopeDeployed()
    {
        return ropeSystem != null && ropeSystem.IsRopeAttached;
    }
    
    // Public method to force release (for tool switching)
    public void ForceRelease()
    {
        if (ropeSystem != null && ropeSystem.IsRopeAttached)
        {
            ropeSystem.ReleaseRope();
        }
    }
}