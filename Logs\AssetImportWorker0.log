Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.1f1 (7197418f847b) revision 7444289'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-07-30T21:46:23Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.1.1f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker0.log
-srvPort
62220
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [24412]  Target information:

Player connection [24412]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2862756217 [EditorId] 2862756217 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24412]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2862756217 [EditorId] 2862756217 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24412]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2862756217 [EditorId] 2862756217 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24412]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2862756217 [EditorId] 2862756217 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24412]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2862756217 [EditorId] 2862756217 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24412] Host joined multi-casting on [***********:54997]...
Player connection [24412] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 412.17 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.1f1 (7197418f847b)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.1.1f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.7688
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56176
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.006592 seconds.
- Loaded All Assemblies, in  0.621 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.441 seconds
Domain Reload Profiling: 1048ms
	BeginReloadAssembly (228ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (90ms)
	LoadAllAssembliesAndSetupDomain (218ms)
		LoadAssemblies (215ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (213ms)
			TypeCache.Refresh (212ms)
				TypeCache.ScanAssembly (194ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (442ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (375ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (73ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (144ms)
			ProcessInitializeOnLoadMethodAttributes (78ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.204 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 20.60 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.888 seconds
Domain Reload Profiling: 5063ms
	BeginReloadAssembly (1383ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (231ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (78ms)
	LoadAllAssembliesAndSetupDomain (1643ms)
		LoadAssemblies (1274ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (554ms)
			TypeCache.Refresh (380ms)
				TypeCache.ScanAssembly (353ms)
			BuildScriptInfoCaches (151ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1888ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1619ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (37ms)
			BeforeProcessingInitializeOnLoad (297ms)
			ProcessInitializeOnLoadAttributes (735ms)
			ProcessInitializeOnLoadMethodAttributes (523ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 22.16 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 549 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7910 unused Assets / (4.6 MB). Loaded Objects now: 8973.
Memory consumption went from 379.0 MB to 374.3 MB.
Total: 16.143100 ms (FindLiveObjects: 1.243500 ms CreateObjectMapping: 1.240700 ms MarkObjects: 9.632200 ms  DeleteObjects: 4.024700 ms)

========================================================================
Received Import Request.
  Time since last request: 191482.708745 seconds.
  path: Assets/_Game/Scripts/PlayerController/SceneTemplateAssets
  artifactKey: Guid(3c576e8cb00fe7f4db01c667ede4bcd6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/PlayerController/SceneTemplateAssets using Guid(3c576e8cb00fe7f4db01c667ede4bcd6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '810f48ddc275c36821ef3f6e1c0e833b') in 0.0469412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.629 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.66 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.439 seconds
Domain Reload Profiling: 3039ms
	BeginReloadAssembly (464ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (175ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (64ms)
	LoadAllAssembliesAndSetupDomain (1004ms)
		LoadAssemblies (830ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (343ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (312ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1440ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1040ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (16ms)
			BeforeProcessingInitializeOnLoad (261ms)
			ProcessInitializeOnLoadAttributes (583ms)
			ProcessInitializeOnLoadMethodAttributes (151ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 22.98 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7906 unused Assets / (2.9 MB). Loaded Objects now: 8998.
Memory consumption went from 316.9 MB to 314.0 MB.
Total: 15.486600 ms (FindLiveObjects: 1.333900 ms CreateObjectMapping: 1.419500 ms MarkObjects: 9.494700 ms  DeleteObjects: 3.236800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.525 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.35 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.304 seconds
Domain Reload Profiling: 2805ms
	BeginReloadAssembly (363ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (1006ms)
		LoadAssemblies (831ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (342ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (312ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1304ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (963ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (235ms)
			ProcessInitializeOnLoadAttributes (558ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (41ms)
Refreshing native plugins compatible for Editor in 23.47 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7906 unused Assets / (2.9 MB). Loaded Objects now: 9000.
Memory consumption went from 313.7 MB to 310.8 MB.
Total: 17.979500 ms (FindLiveObjects: 1.193100 ms CreateObjectMapping: 0.741300 ms MarkObjects: 12.975000 ms  DeleteObjects: 3.068800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.617 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.97 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.322 seconds
Domain Reload Profiling: 2914ms
	BeginReloadAssembly (379ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (1082ms)
		LoadAssemblies (902ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (355ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (325ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1322ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (986ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (237ms)
			ProcessInitializeOnLoadAttributes (588ms)
			ProcessInitializeOnLoadMethodAttributes (133ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 20.97 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7906 unused Assets / (5.6 MB). Loaded Objects now: 9002.
Memory consumption went from 313.7 MB to 308.1 MB.
Total: 23.786800 ms (FindLiveObjects: 1.500800 ms CreateObjectMapping: 1.006100 ms MarkObjects: 17.595000 ms  DeleteObjects: 3.683800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.590 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.11 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.449 seconds
Domain Reload Profiling: 3017ms
	BeginReloadAssembly (415ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (30ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (119ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (1022ms)
		LoadAssemblies (848ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (344ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (314ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1450ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1082ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (15ms)
			BeforeProcessingInitializeOnLoad (293ms)
			ProcessInitializeOnLoadAttributes (611ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (42ms)
Refreshing native plugins compatible for Editor in 23.01 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7906 unused Assets / (2.9 MB). Loaded Objects now: 9004.
Memory consumption went from 313.7 MB to 310.8 MB.
Total: 25.531400 ms (FindLiveObjects: 1.384600 ms CreateObjectMapping: 1.284900 ms MarkObjects: 19.402600 ms  DeleteObjects: 3.457600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 4729.806753 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '418adfc5193b597f24e28e51ee90b6f1') in 0.6255419 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 2.536467 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a4c281fb597063a683b0ee410ead68c3') in 0.005541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 2.581954 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '05293303158502e1997e810583c8702a') in 0.006268 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 3.973463 seconds.
  path: Assets/_Game/Models/Items/Manual.fbx
  artifactKey: Guid(343c771e412698c42b694d578a18772c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Items/Manual.fbx using Guid(343c771e412698c42b694d578a18772c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ce0e01cff9fe3e238b5dad10036fb1d4') in 7.3596575 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '46698f556d0321a1f585e2b706a1c56e') in 0.0052004 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 4.818767 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0ead11f43c71ae01edc7fb3ee6b90626') in 0.0447355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 231

========================================================================
Received Import Request.
  Time since last request: 32.243683 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '220006132b93813c8aec85476b361012') in 0.0286308 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 231

========================================================================
Received Import Request.
  Time since last request: 3.877288 seconds.
  path: Assets/_Game/Models/Items/Manual.fbx
  artifactKey: Guid(343c771e412698c42b694d578a18772c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Items/Manual.fbx using Guid(343c771e412698c42b694d578a18772c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd0641bdb023bae53a306ea0d9e331cc8') in 0.0417483 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 35.588041 seconds.
  path: Assets/_Game/Models/Items/Manual.fbx
  artifactKey: Guid(343c771e412698c42b694d578a18772c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Items/Manual.fbx using Guid(343c771e412698c42b694d578a18772c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5ac961efe2412469d73f63f8d0b7c944') in 0.0764405 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 114.499984 seconds.
  path: Assets/_Game/Models/Items
  artifactKey: Guid(070ffea0cf91ff74e946a3ad81c57c6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Items using Guid(070ffea0cf91ff74e946a3ad81c57c6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c6075b9715c5cac8edf8cf9878c89e3f') in 0.0010827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 41.473504 seconds.
  path: Assets/_Game/Resources/Items/AxeManual.asset
  artifactKey: Guid(1abe8ea745304c345a70a6ea6cff2775) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/AxeManual.asset using Guid(1abe8ea745304c345a70a6ea6cff2775) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cd6879b011225a842b9c71821318716d') in 0.0037717 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 3.302074 seconds.
  path: Assets/Samples/High Definition RP/Common/Prefabs/SceneStyle/HDRP_SceneTemplate_Abstract.prefab
  artifactKey: Guid(8f5a8fbc4e871354682ab0994bbc31fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/High Definition RP/Common/Prefabs/SceneStyle/HDRP_SceneTemplate_Abstract.prefab using Guid(8f5a8fbc4e871354682ab0994bbc31fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3f3f6bf187de2135b2aaae129e6cac08') in 0.0203643 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Samples/High Definition RP/Common/Prefabs/Light/HDRPSamplesFloorSpotlight.prefab
  artifactKey: Guid(fd1c2074ecba2854891106669ac7fc91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/High Definition RP/Common/Prefabs/Light/HDRPSamplesFloorSpotlight.prefab using Guid(fd1c2074ecba2854891106669ac7fc91) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8eb2aced7c398fbc193094010fb3b868') in 0.0038187 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Samples/Core RP Library/Common/Prefabs/UnityBall/UnityMaterialBall_Black.prefab
  artifactKey: Guid(ed4d7156b7ecb474aa517b8e1ad366ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/Core RP Library/Common/Prefabs/UnityBall/UnityMaterialBall_Black.prefab using Guid(ed4d7156b7ecb474aa517b8e1ad366ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c272481ca193a9a33baa00d4f851216') in 0.0035174 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 52

========================================================================
Received Import Request.
  Time since last request: 17.693500 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '94518e13605cf696ca58802ecce02fea') in 0.0417311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 231

========================================================================
Received Import Request.
  Time since last request: 9.957130 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20fffee2bc0b5eea937bf5c3db9335f1') in 0.0381851 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 230

========================================================================
Received Import Request.
  Time since last request: 7.279691 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c18e849c84eeba2c47fad6f90dbe16c8') in 0.0314351 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 231

========================================================================
Received Import Request.
  Time since last request: 2.425408 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '225e7fb2630a877227c01ba4541714b9') in 0.0274867 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 220

========================================================================
Received Import Request.
  Time since last request: 20.394863 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a23b29a21f215747f8745c3d90ce04e5') in 0.0248407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 220

========================================================================
Received Import Request.
  Time since last request: 3.351633 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8a398d527a427b5368ecd51fc967880a') in 0.0222183 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 220

========================================================================
Received Import Request.
  Time since last request: 6.795529 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '73e0eac563957329a6ace53deefaa119') in 0.0233758 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 220

========================================================================
Received Import Request.
  Time since last request: 109.954353 seconds.
  path: Assets/_Game/Resources/ItemsPrefab/AxeName.prefab
  artifactKey: Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsPrefab/AxeName.prefab using Guid(6b7a3e0514f42444fba8ae8fadd2ed2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '32160e939bf7288708281aee162fe2eb') in 0.3688771 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.976755 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/spnza_bricks_a_diff.mat
  artifactKey: Guid(68c7381e823e65642b24ecba1102355f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/spnza_bricks_a_diff.mat using Guid(68c7381e823e65642b24ecba1102355f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db7e3cf52165332f984d1e7b49fe3434') in 0.0454111 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_ceiling_a_diff.mat
  artifactKey: Guid(4698bce29eb2d224e83461a1012f115c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_ceiling_a_diff.mat using Guid(4698bce29eb2d224e83461a1012f115c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff3185bb7d4bcfaac5787aac12244c98') in 0.0417544 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/TextMesh Pro/Shaders/TMP_SDF-HDRP LIT.shadergraph
  artifactKey: Guid(ca2ed216f98028c4dae6c5224a952b3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Shaders/TMP_SDF-HDRP LIT.shadergraph using Guid(ca2ed216f98028c4dae6c5224a952b3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bad0e72398aa8440332704e76e46e79a') in 44.7604433 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_fabric_blue_diff.mat
  artifactKey: Guid(ee9d2c6bf860dab4d8dbdaad76b74278) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_fabric_blue_diff.mat using Guid(ee9d2c6bf860dab4d8dbdaad76b74278) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '53b19452970516c3d6f72c7635584e52') in 0.0742917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/_Game/Materials/GRIDPNG/Orange/Materials/texture_02.mat
  artifactKey: Guid(9b493fed1ebe2854299548af13770f62) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Materials/GRIDPNG/Orange/Materials/texture_02.mat using Guid(9b493fed1ebe2854299548af13770f62) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aedd8e3370bfaa8744ab2056effb869c') in 0.0643632 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/vase_plant.mat
  artifactKey: Guid(251872ad842cea541964f04dd515716b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/vase_plant.mat using Guid(251872ad842cea541964f04dd515716b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f0e1e24030482d9a3732dc3081031d4') in 0.044145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_green.mat
  artifactKey: Guid(5a8e62a64ecfa8a4193dda153235e0e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_green.mat using Guid(5a8e62a64ecfa8a4193dda153235e0e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3a0629a866a84fd7113fc5e1c38fca66') in 0.0705347 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_fabric_green_diff.mat
  artifactKey: Guid(d41e3bd76c0ca2c4bb2b0d8c996e0ff0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_fabric_green_diff.mat using Guid(d41e3bd76c0ca2c4bb2b0d8c996e0ff0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9f293b372ae254786c516ad58914994b') in 0.0535269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_curtain_green_diff.mat
  artifactKey: Guid(b8cfd6cc288d10845ad6af826dd6d2be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_curtain_green_diff.mat using Guid(b8cfd6cc288d10845ad6af826dd6d2be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c2fc136e50b4ba84bd214d2094ba9dcd') in 0.0534555 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_curtain_diff.mat
  artifactKey: Guid(84256d976e5626843b2e0822ad0de2dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_curtain_diff.mat using Guid(84256d976e5626843b2e0822ad0de2dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '940ac9d3e074bfddc8f067c17bde6140') in 0.0572504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000103 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_roof_diff.mat
  artifactKey: Guid(68c778205af6be24c929da9e4baa70fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_roof_diff.mat using Guid(68c778205af6be24c929da9e4baa70fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6932ed78005cdea586458637fc78053e') in 0.0495016 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/vase_dif.mat
  artifactKey: Guid(b2d65aafe04cdc5479912c90153d0b19) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/vase_dif.mat using Guid(b2d65aafe04cdc5479912c90153d0b19) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf655f89e5da3d9bf012e81ecd0c7e48') in 0.0678299 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_flagpole_diff.mat
  artifactKey: Guid(3ab6f16b76beac549ac4a4b2b1cf1289) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_flagpole_diff.mat using Guid(3ab6f16b76beac549ac4a4b2b1cf1289) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bd4ed28859b71cc1c60cbd2b0c2e28b0') in 0.1845458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/vase_round.mat
  artifactKey: Guid(f028aea0d5a15b74b9341e38b8872491) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/vase_round.mat using Guid(f028aea0d5a15b74b9341e38b8872491) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '89b8272249f096214c1877faa4bf0d50') in 0.2215809 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_column_b_diff.mat
  artifactKey: Guid(f4cab645bc39e994e8d4de09f3684dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_column_b_diff.mat using Guid(f4cab645bc39e994e8d4de09f3684dda) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '528694d82b9a38ab2ccf5db34c36d028') in 0.0581613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Samples/High Definition RP/17.0.3/Volumetric Samples/Fog Volume Shadergraph/Volumetric Heart Material.mat
  artifactKey: Guid(55bd530018497aa47a1dc0f1d1e07715) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/High Definition RP/17.0.3/Volumetric Samples/Fog Volume Shadergraph/Volumetric Heart Material.mat using Guid(55bd530018497aa47a1dc0f1d1e07715) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f516dd5cd8bd225c739ebf6148f5e5a6') in 0.1154423 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/vase_hanging.mat
  artifactKey: Guid(4dedd61c8ef4cab428f6c6e290dc5944) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/vase_hanging.mat using Guid(4dedd61c8ef4cab428f6c6e290dc5944) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ed882f7a78614bfd5a22b764903b6936') in 0.1611774 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/_Game/Materials/GRIDPNG/Green/Materials/texture_10.mat
  artifactKey: Guid(9d4947c59452ca842bcc2c415e786049) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Materials/GRIDPNG/Green/Materials/texture_10.mat using Guid(9d4947c59452ca842bcc2c415e786049) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cbe7555cf4ac66d331d8d35ac0dcdc4c') in 0.243359 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/_Game/Shaders/Glitch/Test.shadergraph
  artifactKey: Guid(9e9a68671f07c354689d78928c33334f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Shaders/Glitch/Test.shadergraph using Guid(9e9a68671f07c354689d78928c33334f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e02c35b982df211c74280b36d85bd018') in 0.3448939 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/_Game/Shaders/TIPS/Resources/test.mat
  artifactKey: Guid(4f229d85dd0fb2f4e9fedfd0cbb65f5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Shaders/TIPS/Resources/test.mat using Guid(4f229d85dd0fb2f4e9fedfd0cbb65f5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4eb9b6c3cb50ef6f83cfbd5a0d524d97') in 0.8815627 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.128832 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_details_diff.mat
  artifactKey: Guid(eb794f8bdc2abea46ab0b88e16f21318) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_details_diff.mat using Guid(eb794f8bdc2abea46ab0b88e16f21318) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa8bb36d082da2c3332423fab847892e') in 0.0353372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_thorn_diff.mat
  artifactKey: Guid(8b6eaea6571dc8c4bb6f439c3652bfca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_thorn_diff.mat using Guid(8b6eaea6571dc8c4bb6f439c3652bfca) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1a71518f4974eb8e47f1e65c5261a51c') in 0.0821234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_column_a_diff.mat
  artifactKey: Guid(3de079643dc9def40b423f76b69c7dad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_column_a_diff.mat using Guid(3de079643dc9def40b423f76b69c7dad) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb8ea38090195cb69bf30461276b6f3c') in 0.124449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.515204 seconds.
  path: Assets/_Game/Materials/GRIDPNG/Orange/Materials/texture_10.mat
  artifactKey: Guid(80ba3ed9cdb84714683c2584c542492e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Materials/GRIDPNG/Orange/Materials/texture_10.mat using Guid(80ba3ed9cdb84714683c2584c542492e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8f5911a4144cc90d9c5f44f77a2e1e1a') in 0.2542784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_fabric_diff.mat
  artifactKey: Guid(e7c36098691139c43998d0535476c29c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_fabric_diff.mat using Guid(e7c36098691139c43998d0535476c29c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bb84ce924d0077ffc447922761bb158b') in 0.057497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Bakery/examples/content/sponza/Materials/sponza_floor_a_diff.mat
  artifactKey: Guid(012063806b0030849959f24285672841) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Bakery/examples/content/sponza/Materials/sponza_floor_a_diff.mat using Guid(012063806b0030849959f24285672841) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f60334c529c6cdf6e761faad475edd5e') in 0.1733171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/_Game/Materials/GRIDPNG/Orange/Materials/texture_01.mat
  artifactKey: Guid(9c1d69bbd675f2044b1dd393a30e7d98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Materials/GRIDPNG/Orange/Materials/texture_01.mat using Guid(9c1d69bbd675f2044b1dd393a30e7d98) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c5ddec76bf19aa8ddfbd45fb1fa02047') in 1.6665548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Samples/High Definition RP/17.0.3/Volumetric Samples/Fog Volume Shadergraph/Volumetric Heart.shadergraph
  artifactKey: Guid(31c75348c6538d447a918cbc5ec8c202) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/High Definition RP/17.0.3/Volumetric Samples/Fog Volume Shadergraph/Volumetric Heart.shadergraph using Guid(31c75348c6538d447a918cbc5ec8c202) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3d3799e7106c670528966e37ea5c7d7c') in 0.0390225 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.32 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 20 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (2.9 MB). Loaded Objects now: 11450.
Memory consumption went from 390.1 MB to 387.2 MB.
Total: 92.871300 ms (FindLiveObjects: 2.330700 ms CreateObjectMapping: 1.222200 ms MarkObjects: 86.104200 ms  DeleteObjects: 3.212200 ms)

Prepare: number of updated asset objects reloaded= 0
