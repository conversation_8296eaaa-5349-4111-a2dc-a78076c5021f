; FBX 7.3.0 project file
; Created by the Blockbench FBX Exporter
; ----------------------------------------------------
; 

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	EncryptionType: 0
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 3
		Day: 2
		Hour: 4
		Minute: 26
		Second: 44
		Millisecond: 413
	}
	Creator: "Blockbench 4.12.3"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "C:\Users\<USER>\foobar.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "C:\Users\<USER>\foobar.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Blockbench"
			P: "Original|ApplicationName", "KString", "", "", "Blockbench FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "4.12.3"
			P: "Original|DateTime_GMT", "DateTime", "", "", "01/01/1970 00:00:00.000"
			P: "Original|FileName", "KString", "", "", "/foobar.fbx"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Blockbench"
			P: "LastSaved|ApplicationName", "KString", "", "", "Blockbench FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "4.12.3"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "01/01/1970 00:00:00.000"
			P: "Original|ApplicationNativeFile", "KString", "", "", ""
		}
	}
}
FileId: "iVFoobar"
CreationTime: "2025-03-02 03:26:44:413"
Creator: "Made with Blockbench"
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",24
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: ********* {
		Scene: "Scene"
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References: 

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 11
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 2
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",5
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 2
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 2
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 2
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "Path", "KString", "XRefUrl", "", ""
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "InterlaceMode", "enum", "", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 44566095, "Geometry::cuboid_1", "Mesh" {
		Vertices: *24 {
			a: 12.5,37.5,12.5,12.5,37.5,-12.5,12.5,12.5,12.5,12.5,12.5,-12.5,-12.5,37.5,12.5,-12.5,37.5,-12.5,-12.5,12.5,12.5,-12.5,12.5,-12.5
		}
		PolygonVertexIndex: *24 {
			a: 1,0,2,-4,6,4,5,-8,4,0,1,-6,3,2,6,-8,2,0,4,-7,5,1,3,-8
		}
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "Direct"
			Normals: *18 {
				a: 1,0,0,-1,0,0,0,1,0,0,-1,0,0,0,1,0,0,-1
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			UV: *48 {
				a: 0.25,1,0,1,0,0.75,0.25,0.75,0.25,0.4375,0.25,0.6875,0,0.6875,0,0.4375,0.3125,0.75,0.5625,0.75,0.5625,1,0.3125,1,0.5625,0.4375,0.5625,0.6875,0.3125,0.6875,0.3125,0.4375,0.25,0.125,0.25,0.375,0,0.375,0,0.125,0.875,1,0.625,1,0.625,0.75,0.875,0.75
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			}
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Geometry: 75200662, "Geometry::cuboid", "Mesh" {
		Vertices: *24 {
			a: 50,37.5,12.5,50,37.5,-12.5,50,12.5,12.5,50,12.5,-12.5,-50,37.5,12.5,-50,37.5,-12.5,-50,12.5,12.5,-50,12.5,-12.5
		}
		PolygonVertexIndex: *24 {
			a: 1,0,2,-4,6,4,5,-8,4,0,1,-6,3,2,6,-8,2,0,4,-7,5,1,3,-8
		}
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "Direct"
			Normals: *18 {
				a: 1,0,0,-1,0,0,0,1,0,0,-1,0,0,0,1,0,0,-1
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			UV: *48 {
				a: 0.65625,1,0.53125,1,0.53125,0.875,0.65625,0.875,0.65625,0.71875,0.65625,0.84375,0.53125,0.84375,0.53125,0.71875,0,0.875,0.5,0.875,0.5,1,0,1,0.5,0.71875,0.5,0.84375,0,0.84375,0,0.71875,0.5,0.5625,0.5,0.6875,0,0.6875,0,0.5625,0.5,0.53125,0,0.53125,0,0.40625,0.5,0.40625
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			}
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 90405613, "Model::cuboid", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "Lcl Translation", "Lcl Translation", "", "A",-6.25,12.5,0
			P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
			P: "RotationOrder", "enum", "", "",5
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: Y
		Culling: "CullingOff"
	}
	Model: 56015929, "Model::cuboid_1", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "Lcl Translation", "Lcl Translation", "", "A",56.25,12.5,0
			P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
			P: "RotationOrder", "enum", "", "",5
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: Y
		Culling: "CullingOff"
	}
	Material: 69371452, "Material::texture", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0.2,0.2,0.2
			P: "Diffuse", "Vector3D", "Vector", "",0.8,0.8,0.8
			P: "Opacity", "double", "Number", "",1
		}
	}
	Texture: 77146989, "Texture::texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::texture"
		Media: "Video::texture"
		FileName: ""
		RelativeFilename: ""
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
	Video: 59319452, "Video::texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "texture"
		}
		UseMipMap: 0
		Filename: ""
		RelativeFilename: ""
		Content: "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAGBJREFUOE9j/P///38GJMDIyMhIrBhIG9GKsRk6SAxA9j85bMYvbW0ogchTVYU1XHAZDjaAW1EOLP/1/iOGgTEA2Xkku4CcgENJeLgSCLEGkxTi2AwdBAYQ61ecCYlSAwBbYGYRc2pdQAAAAABJRU5ErkJggg=="
	}
	Material: 69934707, "Material::texture_1", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0.2,0.2,0.2
			P: "Diffuse", "Vector3D", "Vector", "",0.8,0.8,0.8
			P: "Opacity", "double", "Number", "",1
		}
	}
	Texture: 58923341, "Texture::texture_1", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::texture_1"
		Media: "Video::texture_1"
		FileName: ""
		RelativeFilename: ""
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
	Video: 30885602, "Video::texture_1", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "texture"
		}
		UseMipMap: 0
		Filename: ""
		RelativeFilename: ""
		Content: "iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAO5JREFUWEdj3HLl4X8GCoCPjjyj84teFDP2ShQzEmskI8gBFioyxKpHUXfizhMGmAMsxMXAcidevmIYeg4gy/tQTRRHASWWU0MvY3BiCUWJcO38HsYvbW0oZvBUVRGfCEEOkBaXIMszT1++YIA5gFtRDmzG1/uPGIaeA8jyPlQTxVFAieXU0Mv4424RRYmQQ7mP6ASHzcFgB7BLq5PlmZ9PbzIMDweQ5X2oJopDgBLLqaGXMXrPF4oS4VIXHsoSIcgBChLcZHnmwYuvDMPDAWR5H6qJ4hCgxHJq6KUoAY06YDQERkNgNARGQ2BYhAAAAp2EIcF5LV4AAAAASUVORK5CYII="
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	C: "OO",90405613,0
	C: "OO",75200662,90405613
	C: "OO",69934707,90405613
	C: "OO",56015929,0
	C: "OO",44566095,56015929
	C: "OO",69371452,56015929
	C: "OP",77146989,69371452, "DiffuseColor"
	C: "OO",59319452,77146989
	C: "OP",58923341,69934707, "DiffuseColor"
	C: "OO",30885602,58923341
}

; Takes section
;------------------------------------------------------------------

Takes:  {
	Current: ""
}
