using UnityEngine;
using System.Collections;
using KinematicCharacterController.FPS;
using AudioSystem;

public class ClimbingRopeSystem : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private Transform gunTip;
    [SerializeField] private Transform camera;
    [SerializeField] private Transform player;
    [SerializeField] private LayerMask grapplableMask;
    [SerializeField] private LineRenderer lineRenderer;
    [SerializeField] private FPSCharacterController playerMovement;
    [SerializeField] private KinematicCharacterMotor motor;
    [SerializeField] private KinematicWallRun wallRunSystem;
    [SerializeField] private PredictionVisualizer predictionVisualizer;
    [SerializeField] private Player<PERSON>udio<PERSON><PERSON><PERSON> player<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
    
    [Header("Rope Settings")]
    [SerializeField] private float maxRopeLength = 30f;
    [SerializeField] private float minRopeLength = 2f;
    [SerializeField] private float ropeThrowSpeed = 40f;
    [SerializeField] private float ropeRetractSpeed = 0.3f; // Time to fully retract
    
    [Header("Climbing Settings")]
    [SerializeField] private float climbSpeed = 4f;
    [SerializeField] private float reelInSpeed = 6f;
    [SerializeField] private float reelOutSpeed = 4f;
    [SerializeField] private float ropeSwayDamping = 8f;
    [SerializeField] private float maxSwayAngle = 30f;
    
    [Header("Movement Integration")]
    [SerializeField] private float wallRunAssistForce = 15f;
    [SerializeField] private float ropeWallOffset = 0.5f;
    [SerializeField] private float detachForce = 20f; // Force needed to detach rope
    
    [Header("Rope Visuals")]
    [SerializeField] private AnimationCurve ropeSwayProfile;
    [SerializeField] private int ropeSegments = 20;
    [SerializeField] private float ropeWidth = 0.05f;
    
    // State tracking
    private bool isRopeAttached = false;
    private bool isClimbingRope = false;
    private bool isRopeDeploying = false;
    private Vector3 ropeAttachPoint;
    private float currentRopeLength;
    private float playerPositionOnRope; // 0 = bottom, 1 = top
    private Vector3 ropeSwayOffset;
    private float ropeDeployProgress;
    
    // Hook projectile
    private GameObject hookProjectile;
    private Rigidbody hookRigidbody;
    private bool hookLaunched = false;
    
    // Rope physics
    private Vector3 lastPlayerVelocity;
    private float currentTension;
    
    public bool IsRopeAttached => isRopeAttached;
    public bool IsClimbingRope => isClimbingRope;
    public Vector3 RopeAttachPoint => ropeAttachPoint;
    
    private void Awake()
    {
        SetupReferences();
        SetupLineRenderer();
        SetupRopeSwayProfile();
        CreateHookProjectile();
    }
    
    private void SetupReferences()
    {
        if (motor == null)
            motor = GetComponent<KinematicCharacterMotor>();
            
        if (playerMovement == null)
            playerMovement = GetComponent<FPSCharacterController>();
            
        if (wallRunSystem == null)
            wallRunSystem = GetComponent<KinematicWallRun>();
            
        if (camera == null && Camera.main != null)
            camera = Camera.main.transform;
            
        if (player == null)
            player = transform;
            
        if (playerAudioHandler == null)
            playerAudioHandler = GetComponent<PlayerAudioHandler>();
    }
    
    private void SetupLineRenderer()
    {
        if (lineRenderer == null)
        {
            lineRenderer = gameObject.AddComponent<LineRenderer>();
        }
        
        lineRenderer.startWidth = ropeWidth;
        lineRenderer.endWidth = ropeWidth;
        lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
        lineRenderer.startColor = new Color(0.4f, 0.3f, 0.2f); // Brown rope color
        lineRenderer.endColor = new Color(0.3f, 0.2f, 0.1f);
        lineRenderer.positionCount = 0;
        lineRenderer.textureMode = LineTextureMode.Tile;
    }
    
    private void SetupRopeSwayProfile()
    {
        if (ropeSwayProfile == null || ropeSwayProfile.keys.Length == 0)
        {
            ropeSwayProfile = new AnimationCurve();
            ropeSwayProfile.AddKey(0f, 0f);
            ropeSwayProfile.AddKey(0.3f, 1f);
            ropeSwayProfile.AddKey(0.7f, 0.8f);
            ropeSwayProfile.AddKey(1f, 0.2f);
        }
    }
    
    private void CreateHookProjectile()
    {
        hookProjectile = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        hookProjectile.name = "GrapplingHook";
        hookProjectile.transform.localScale = Vector3.one * 0.2f;
        hookProjectile.SetActive(false);
        
        // Setup rigidbody for physics-based throw
        hookRigidbody = hookProjectile.AddComponent<Rigidbody>();
        hookRigidbody.mass = 0.5f;
        hookRigidbody.drag = 0.5f;
        hookRigidbody.angularDrag = 0.5f;
        hookRigidbody.useGravity = true;
        hookRigidbody.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
        
        // Add collider trigger
        SphereCollider hookCollider = hookProjectile.GetComponent<SphereCollider>();
        hookCollider.isTrigger = true;
        
        // Add hook collision handler
        HookCollisionHandler hookHandler = hookProjectile.AddComponent<HookCollisionHandler>();
        hookHandler.Initialize(this, grapplableMask);
    }
    
    private void Update()
    {
        HandleInput();
        UpdateRopePhysics();
        UpdateRopeVisuals();
        
        if (isRopeDeploying)
        {
            UpdateRopeDeployment();
        }
    }
    
    private void HandleInput()
    {
        // Only handle climbing input when rope is attached
        if (isRopeAttached && !isRopeDeploying)
        {
            // Vertical climbing
            if (Input.GetKey(KeyCode.W))
            {
                ClimbRope(1f);
            }
            else if (Input.GetKey(KeyCode.S))
            {
                ClimbRope(-1f);
            }
            
            // Reel in/out
            if (Input.GetKey(KeyCode.Space))
            {
                AdjustRopeLength(-reelInSpeed * Time.deltaTime);
            }
            else if (Input.GetKey(KeyCode.LeftControl))
            {
                AdjustRopeLength(reelOutSpeed * Time.deltaTime);
            }
            
            // Start climbing mode with Shift
            if (Input.GetKeyDown(KeyCode.LeftShift) && !isClimbingRope)
            {
                StartClimbingMode();
            }
            else if (Input.GetKeyUp(KeyCode.LeftShift) && isClimbingRope)
            {
                StopClimbingMode();
            }
        }
    }
    
    public void FireRope()
    {
        if (isRopeAttached || hookLaunched) return;
        
        // Check for valid target
        bool hasTarget = false;
        Vector3 targetPoint = Vector3.zero;
        
        if (predictionVisualizer != null && predictionVisualizer.HasPredictionPoint())
        {
            hasTarget = true;
            targetPoint = predictionVisualizer.GetPredictionPoint();
        }
        else
        {
            // Fallback to simple raycast
            RaycastHit hit;
            if (Physics.Raycast(camera.position, camera.forward, out hit, maxRopeLength, grapplableMask))
            {
                hasTarget = true;
                targetPoint = hit.point;
            }
        }
        
        if (hasTarget)
        {
            LaunchHook(targetPoint);
        }
    }
    
    private void LaunchHook(Vector3 targetPoint)
    {
        hookLaunched = true;
        hookProjectile.SetActive(true);
        hookProjectile.transform.position = gunTip.position;
        
        // Calculate launch velocity
        Vector3 direction = (targetPoint - gunTip.position).normalized;
        hookRigidbody.velocity = direction * ropeThrowSpeed;
        
        // Play launch sound
        if (playerAudioHandler != null)
        {
            playerAudioHandler.OnGrappleFire();
        }
        
        // Start showing rope
        isRopeDeploying = true;
        ropeDeployProgress = 0f;
        lineRenderer.positionCount = 2;
    }
    
    public void OnHookHit(Vector3 hitPoint, Collider hitCollider)
    {
        if (!hookLaunched) return;
        
        hookLaunched = false;
        isRopeDeploying = false;
        isRopeAttached = true;
        ropeAttachPoint = hitPoint;
        
        // Disable hook physics and parent to hit object
        hookRigidbody.isKinematic = true;
        hookProjectile.transform.position = hitPoint;
        if (hitCollider.transform != null)
        {
            hookProjectile.transform.SetParent(hitCollider.transform);
        }
        
        // Calculate initial rope length
        currentRopeLength = Vector3.Distance(player.position, ropeAttachPoint);
        currentRopeLength = Mathf.Clamp(currentRopeLength, minRopeLength, maxRopeLength);
        
        // Initialize climbing position (start at bottom)
        playerPositionOnRope = 0f;
        
        // If wall running, provide assist
        if (wallRunSystem != null && wallRunSystem.isWallRunning)
        {
            AssistWallRun();
        }
    }
    
    public void ReleaseRope()
    {
        if (!isRopeAttached) return;
        
        StartCoroutine(RetractRope());
    }
    
    private IEnumerator RetractRope()
    {
        isRopeAttached = false;
        isClimbingRope = false;
        
        // Detach hook
        hookProjectile.transform.SetParent(null);
        hookRigidbody.isKinematic = false;
        
        // Animate rope retraction
        float retractTime = 0f;
        Vector3 startPos = hookProjectile.transform.position;
        
        while (retractTime < ropeRetractSpeed)
        {
            retractTime += Time.deltaTime;
            float t = retractTime / ropeRetractSpeed;
            
            hookProjectile.transform.position = Vector3.Lerp(startPos, gunTip.position, t);
            UpdateRopeVisuals();
            
            yield return null;
        }
        
        // Hide everything
        hookProjectile.SetActive(false);
        lineRenderer.positionCount = 0;
    }
    
    private void UpdateRopeDeployment()
    {
        if (!isRopeDeploying) return;
        
        ropeDeployProgress += Time.deltaTime * 2f;
        ropeDeployProgress = Mathf.Clamp01(ropeDeployProgress);
        
        // Update rope visual during deployment
        UpdateRopeVisuals();
    }
    
    private void ClimbRope(float direction)
    {
        if (!isClimbingRope) return;
        
        // Update position on rope
        playerPositionOnRope += direction * (climbSpeed / currentRopeLength) * Time.deltaTime;
        playerPositionOnRope = Mathf.Clamp01(playerPositionOnRope);
        
        // Calculate target position
        Vector3 bottomPos = ropeAttachPoint - (Vector3.up * currentRopeLength);
        Vector3 targetPos = Vector3.Lerp(bottomPos, ropeAttachPoint, playerPositionOnRope);
        
        // Apply position change through velocity
        Vector3 moveDirection = (targetPos - player.position);
        float moveDistance = moveDirection.magnitude;
        
        if (moveDistance > 0.01f)
        {
            playerMovement.BaseVelocity = moveDirection.normalized * climbSpeed;
        }
    }
    
    private void AdjustRopeLength(float delta)
    {
        currentRopeLength += delta;
        currentRopeLength = Mathf.Clamp(currentRopeLength, minRopeLength, maxRopeLength);
    }
    
    private void StartClimbingMode()
    {
        isClimbingRope = true;
        
        // Disable gravity while climbing
        motor.SetGroundSolvingActivation(false);
        
        // Lock player to rope constraint
        Vector3 ropeDirection = (ropeAttachPoint - player.position).normalized;
        float distanceToAttachPoint = Vector3.Distance(player.position, ropeAttachPoint);
        
        // Set initial position on rope based on current distance
        playerPositionOnRope = 1f - (distanceToAttachPoint / currentRopeLength);
        playerPositionOnRope = Mathf.Clamp01(playerPositionOnRope);
    }
    
    private void StopClimbingMode()
    {
        isClimbingRope = false;
        motor.SetGroundSolvingActivation(true);
    }
    
    private void UpdateRopePhysics()
    {
        if (!isRopeAttached) return;
        
        // Calculate tension based on player movement
        Vector3 ropeDirection = (ropeAttachPoint - player.position).normalized;
        float velocityAlongRope = Vector3.Dot(playerMovement.Velocity, ropeDirection);
        
        // Apply constraint if moving away from rope attach point
        float currentDistance = Vector3.Distance(player.position, ropeAttachPoint);
        if (currentDistance > currentRopeLength && velocityAlongRope < 0)
        {
            // Apply rope constraint force
            float overExtension = currentDistance - currentRopeLength;
            Vector3 constraintForce = ropeDirection * overExtension * 50f;
            playerMovement.AddVelocity(constraintForce * Time.deltaTime);
            
            // Check if force is too high (rope should break)
            if (overExtension > 5f)
            {
                ReleaseRope();
            }
        }
        
        // Update rope sway
        Vector3 playerVelocity = playerMovement.Velocity;
        Vector3 lateralVelocity = Vector3.ProjectOnPlane(playerVelocity, ropeDirection);
        ropeSwayOffset = Vector3.Lerp(ropeSwayOffset, lateralVelocity * 0.1f, Time.deltaTime * ropeSwayDamping);
        ropeSwayOffset = Vector3.ClampMagnitude(ropeSwayOffset, maxSwayAngle * Mathf.Deg2Rad * currentRopeLength);
    }
    
    private void UpdateRopeVisuals()
    {
        if (!hookProjectile.activeSelf)
        {
            lineRenderer.positionCount = 0;
            return;
        }
        
        Vector3 startPoint = gunTip.position;
        Vector3 endPoint = hookProjectile.transform.position;
        
        // Update segment count based on distance
        float ropeDistance = Vector3.Distance(startPoint, endPoint);
        int segmentCount = Mathf.Max(2, Mathf.RoundToInt(ropeDistance * 2f));
        lineRenderer.positionCount = segmentCount;
        
        // Generate rope curve with physics-based sway
        for (int i = 0; i < segmentCount; i++)
        {
            float t = i / (float)(segmentCount - 1);
            Vector3 point = Vector3.Lerp(startPoint, endPoint, t);
            
            // Add sway
            if (isRopeAttached && !isRopeDeploying)
            {
                float swayAmount = ropeSwayProfile.Evaluate(t);
                point += ropeSwayOffset * swayAmount;
                
                // Add slight gravity sag
                float sagAmount = Mathf.Sin(t * Mathf.PI) * 0.5f;
                point += Vector3.down * sagAmount * (1f - currentTension);
            }
            
            lineRenderer.SetPosition(i, point);
        }
    }
    
    private void AssistWallRun()
    {
        // Calculate optimal wall run direction
        Vector3 ropeToPlayer = (player.position - ropeAttachPoint).normalized;
        Vector3 wallRunDirection = Vector3.Cross(ropeToPlayer, Vector3.up).normalized;
        
        // Apply assist force
        playerMovement.AddVelocity(wallRunDirection * wallRunAssistForce);
    }
    
    public void SetPredictionEnabled(bool enabled)
    {
        // This method allows the tool system to enable/disable prediction
        // You can add prediction visualization logic here if needed
        if (predictionVisualizer != null)
        {
            predictionVisualizer.enabled = enabled;
        }
    }
    
    // Inner class for hook collision detection
    private class HookCollisionHandler : MonoBehaviour
    {
        private ClimbingRopeSystem ropeSystem;
        private LayerMask grapplableMask;
        
        public void Initialize(ClimbingRopeSystem system, LayerMask mask)
        {
            ropeSystem = system;
            grapplableMask = mask;
        }
        
        private void OnTriggerEnter(Collider other)
        {
            // Check if hit object is grapplable
            if (((1 << other.gameObject.layer) & grapplableMask.value) != 0)
            {
                ropeSystem.OnHookHit(transform.position, other);
            }
        }
    }
}