Base path: 'C:/Unity/Editors/6000.1.1f1/Editor/Data', plugins path 'C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=31339 file=Packages/com.unity.render-pipelines.high-definition/Editor/Tools/Resources/ColorCheckerShader.shadergraph name=Hidden/ColorCheckerShader pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=19 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=DOTS_INSTANCING_ON dKW=INSTANCING_ON _DOUBLESIDED_ON _REFRACTION_PLANE _REFRACTION_SPHERE _REFRACTION_THIN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=0 lang=3 type=Vertex platform=d3d11 reqs=1101803 mask=6 start=95 ok=1 outsize=1566

Cmd: compileSnippet
  insize=48518 file=Packages/com.unity.render-pipelines.high-definition/Editor/Tools/Resources/ColorCheckerShader.shadergraph name=Hidden/ColorCheckerShader pass=SceneSelectionPass ppOnly=0 stripLineD=0 buildPlatform=19 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW= dKW=DOTS_INSTANCING_ON INSTANCING_ON _DOUBLESIDED_ON _REFRACTION_PLANE _REFRACTION_SPHERE _REFRACTION_THIN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=0 lang=3 type=Vertex platform=d3d11 reqs=1101803 mask=6 start=3035  error: 1 plat 4 at C:/Unity/BLAME/BLAME/Library/PackageCache/com.unity.render-pipelines.high-definition@78ce765e749f/Editor/Tools/Resources/ProceduralColorchecker.hlsl(45): implicit truncation of vector type
 ok=1 outsize=4238

Cmd: compileSnippet
  insize=52088 file=Assets/Bakery/shader/ShaderGraph/HDRP/BakerySHGraph.shadergraph name=Shader Graphs/BakerySHGraph pass=SceneSelectionPass ppOnly=0 stripLineD=0 buildPlatform=19 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW= dKW=DOTS_INSTANCING_ON INSTANCING_ON _DOUBLESIDED_ON _REFRACTION_PLANE _REFRACTION_SPHERE _REFRACTION_THIN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=0 lang=3 type=Vertex platform=d3d11 reqs=1101803 mask=6 start=3039  error: 1 plat 4 at Assets/Bakery/shader/ShaderGraph/HDRP/BakeryDecodeLightmap.hlsl(53): implicit truncation of vector type
  error: 1 plat 4 at Assets/Bakery/shader/ShaderGraph/HDRP/BakeryDecodeLightmap.hlsl(58): implicit truncation of vector type
  error: 1 plat 4 at Assets/Bakery/shader/ShaderGraph/HDRP/BakeryDecodeLightmap.hlsl(63): implicit truncation of vector type
  error: 1 plat 4 at Assets/Bakery/shader/ShaderGraph/HDRP/BakeryDecodeLightmap.hlsl(149): implicit truncation of vector type
  error: 1 plat 4 at Assets/Bakery/shader/ShaderGraph/HDRP/BakeryDecodeLightmap.hlsl(150): implicit truncation of vector type
  error: 1 plat 4 at Assets/Bakery/shader/ShaderGraph/HDRP/BakeryDecodeLightmap.hlsl(151): implicit truncation of vector type
  error: 1 plat 4 at Assets/Bakery/shader/ShaderGraph/HDRP/BakeryDecodeLightmap.hlsl(203): implicit truncation of vector type
  error: 1 plat 4 at Assets/Bakery/shader/ShaderGraph/HDRP/BakeryDecodeLightmap.hlsl(204): implicit truncation of vector type
  error: 1 plat 4 at Assets/Bakery/shader/ShaderGraph/HDRP/BakeryDecodeLightmap.hlsl(205): implicit truncation of vector type
  error: 1 plat 4 at Assets/Bakery/shader/ShaderGraph/HDRP/BakeryDecodeLightmap.hlsl(487): implicit truncation of vector type
 ok=1 outsize=4422

