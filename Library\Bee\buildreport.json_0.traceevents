{ "pid": 102376, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 102376, "tid": 1, "ts": 1753916137970430, "dur": 13778, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 102376, "tid": 1, "ts": 1753916137984214, "dur": 730396, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 102376, "tid": 1, "ts": 1753916138714620, "dur": 13172, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 102376, "tid": 1858, "ts": 1753916147452674, "dur": 1516, "ph": "X", "name": "", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916137967691, "dur": 20202, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916137987896, "dur": 9449880, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916137988925, "dur": 3191, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916137992124, "dur": 667, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916137992794, "dur": 6939, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916137999741, "dur": 276, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916138000020, "dur": 117, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916138000139, "dur": 715, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916138000857, "dur": 17330, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916138018287, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916138018292, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916138018329, "dur": 46, "ph": "X", "name": "ReadAsync 82", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916138018379, "dur": 312, "ph": "X", "name": "ProcessMessages 1", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916138018694, "dur": 116, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916138018813, "dur": 659, "ph": "X", "name": "ProcessMessages 388", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916138019474, "dur": 3723173, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916141742653, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916141742656, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916141742686, "dur": 691, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916141743380, "dur": 5673799, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916147417188, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916147417191, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916147417226, "dur": 1886, "ph": "X", "name": "ProcessMessages 15370", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916147419116, "dur": 5349, "ph": "X", "name": "ReadAsync 15370", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916147424471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916147424474, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916147424500, "dur": 306, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916147424810, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916147424824, "dur": 401, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 102376, "tid": 12884901888, "ts": 1753916147425228, "dur": 11873, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 102376, "tid": 1858, "ts": 1753916147454195, "dur": 49, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 102376, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 102376, "tid": 8589934592, "ts": 1753916137961584, "dur": 766280, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 102376, "tid": 8589934592, "ts": 1753916138727867, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 102376, "tid": 8589934592, "ts": 1753916138727873, "dur": 1382, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 102376, "tid": 1858, "ts": 1753916147454245, "dur": 105, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 102376, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 102376, "tid": 4294967296, "ts": 1753916137936856, "dur": 9502386, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 102376, "tid": 4294967296, "ts": 1753916137941716, "dur": 14303, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 102376, "tid": 4294967296, "ts": 1753916147439299, "dur": 4568, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 102376, "tid": 4294967296, "ts": 1753916147442241, "dur": 31, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 102376, "tid": 4294967296, "ts": 1753916147443958, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 102376, "tid": 1858, "ts": 1753916147454353, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1753916137986569, "dur":31354, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753916138017935, "dur":134, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753916138018095, "dur":576, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753916138018712, "dur":229, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ScriptAssembliesAndTypeDB" }}
,{ "pid":12345, "tid":0, "ts":1753916138018704, "dur":286, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753916138018991, "dur":9405819, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753916147424812, "dur":240, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753916147425236, "dur":56, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753916147425354, "dur":4834, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1753916138018797, "dur":201, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753916138020470, "dur":446687, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916138467314, "dur":34045, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916138501545, "dur":130521, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916138632209, "dur":209543, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916138841939, "dur":30478, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916138872592, "dur":91466, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916138964247, "dur":131599, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139096016, "dur":78076, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139174391, "dur":1030, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139175565, "dur":79120, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139254877, "dur":37407, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139292443, "dur":93244, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.PerformanceTesting.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139385930, "dur":53735, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139439794, "dur":274942, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139714879, "dur":33660, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139748689, "dur":74806, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139823689, "dur":56326, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139880241, "dur":34303, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139914734, "dur":36621, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139951531, "dur":27096, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916139978808, "dur":141544, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916140120524, "dur":168604, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916140289308, "dur":32392, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916140321911, "dur":63364, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916140385497, "dur":28406, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916140414086, "dur":130552, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916140544782, "dur":26869, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916140571832, "dur":239796, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916140811832, "dur":28449, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916140840426, "dur":26116, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916140866720, "dur":180491, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916141047353, "dur":74538, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916141122056, "dur":146210, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916141268458, "dur":82387, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916141351025, "dur":97552, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916141448768, "dur":47928, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916141496873, "dur":105769, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\UnityEngine.TestRunner.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916141602871, "dur":137742, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1753916138019014, "dur":3723240, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1753916141744223, "dur":5674606, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1753916138019264, "dur":9405538, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753916138019324, "dur":9405453, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753916138018912, "dur":99, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753916138019037, "dur":722462, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753916138741500, "dur":8683283, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753916138018941, "dur":84, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753916138019030, "dur":710755, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753916138740964, "dur":527, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":5, "ts":1753916138729786, "dur":11710, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753916138741496, "dur":8683307, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753916138019361, "dur":9405457, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753916138019426, "dur":9405386, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753916138019383, "dur":9405395, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753916138019520, "dur":9405294, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753916138019067, "dur":9405713, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753916138019292, "dur":9405484, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753916138019555, "dur":9405249, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753916147436522, "dur":615, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 102376, "tid": 1858, "ts": 1753916147454883, "dur": 3996, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 102376, "tid": 1858, "ts": 1753916147459035, "dur": 2271, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 102376, "tid": 1858, "ts": 1753916147451822, "dur": 10360, "ph": "X", "name": "Write chrome-trace events", "args": {} },
