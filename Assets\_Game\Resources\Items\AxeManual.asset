%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8ceba65529477cd4f940f3d4d245f94a, type: 3}
  m_Name: AxeManual
  m_EditorClassIdentifier: 
  itemName: AxeName
  Description: AxeName1
  Price: 10
  Icon: {fileID: 21300000, guid: e313fba2d45416d43a83bb9ed7202f28, type: 3}
  WorldModelPrefab: {fileID: 919132149155446097, guid: 343c771e412698c42b694d578a18772c, type: 3}
  slotDimension:
    width: 1
    height: 1
  maxStack: 1
  isHorizontal: 1
  Weight: 1
  EnergyRestore: 0
  HealthRestore: 0
  itemInformationList:
  - targetItemName: Axe
    itemNameToApply: Axetest
    itemDescriptionToApply: "\u2395\u2395\u2395\u2395\r\n\u258A\u258A\u258A\u258A\r\r"
  showIndividualNotifications: 1
  useMessage: You study the manual carefully...
  completionMessage: You learned about {0} item(s)!
