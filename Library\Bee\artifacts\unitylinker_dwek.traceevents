{ "pid": 84728, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 84728, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 84728, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 84728, "tid": 1, "ts": 1753916188355069, "dur": 3901276, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 84728, "tid": 1, "ts": 1753916188357593, "dur": 445351, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 84728, "tid": 1, "ts": 1753916188714241, "dur": 86348, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 84728, "tid": 1, "ts": 1753916188876219, "dur": 16740, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 84728, "tid": 1, "ts": 1753916188894514, "dur": 216176, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 84728, "tid": 1, "ts": 1753916189110748, "dur": 2822246, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 84728, "tid": 1, "ts": 1753916191933021, "dur": 303101, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 84728, "tid": 1, "ts": 1753916192252434, "dur": 3648, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 84728, "tid": 1, "ts": 1753916192256347, "dur": 488, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 84728, "tid": 1, "ts": 1753916192267757, "dur": 3614, "ph": "X", "name": "", "args": {} },
{ "pid": 84728, "tid": 1, "ts": 1753916192265768, "dur": 6119, "ph": "X", "name": "Write chrome-trace events", "args": {} },
