#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

[System.Serializable]
public class WirePoint
{
    public Vector3 position;
    public Vector3 oldPosition;
    public Vector3 velocity;
    public bool isFixed;
    public float mass = 1f;
    
    public WirePoint(Vector3 pos, bool fix = false, float pointMass = 1f)
    {
        position = pos;
        oldPosition = pos;
        velocity = Vector3.zero;
        isFixed = fix;
        mass = pointMass;
    }
}

[System.Serializable]
public class WireSegment
{
    public int pointA;
    public int pointB;
    public float restLength;
    public float stiffness = 1f;
    
    public WireSegment(int a, int b, float length, float segmentStiffness = 1f)
    {
        pointA = a;
        pointB = b;
        restLength = length;
        stiffness = segmentStiffness;
    }
}

[System.Serializable]
public class WirePreset
{
    public string name;
    public float radius = 0.05f;
    public int resolution = 8;
    public float gravity = -9.81f;
    public float stiffness = 0.8f;
    public float damping = 0.99f;
    public int iterations = 10;
    public float slackFactor = 1.5f;
    public Material material;
}

public class WireCreatorWindow : EditorWindow
{
    #region Serialized Fields
    [SerializeField] private List<WirePoint> wirePoints = new List<WirePoint>();
    [SerializeField] private List<WireSegment> wireSegments = new List<WireSegment>();
    [SerializeField] private List<Vector3> originalPoints = new List<Vector3>();
    [SerializeField] private List<WirePreset> presets = new List<WirePreset>();
    #endregion

    #region Wire Parameters
    private float wireRadius = 0.05f;
    private int wireResolution = 8;
    private float gravity = -9.81f;
    private float stiffness = 0.8f;
    private float damping = 0.99f;
    private int simulationIterations = 10;
    private int subdivisions = 5;
    private bool autoSubdivide = true;
    private float slackFactor = 1.5f;
    private AnimationCurve slackDistribution = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
    private float windStrength = 0f;
    private Vector3 windDirection = Vector3.right;
    private float airResistance = 0.01f;
    #endregion

    #region Editor State
    private bool isSimulating = false;
    private bool isPlacingPoints = false;
    private bool showAdvancedSettings = false;
    private bool showPresets = false;
    private double lastSimulationTime;
    private Material wireMaterial;
    private GameObject previewObject;
    private MeshFilter previewMeshFilter;
    private MeshRenderer previewMeshRenderer;
    private int selectedPresetIndex = -1;
    private string newPresetName = "New Preset";
    #endregion

    #region Mesh Generation Cache
    private List<Vector3> vertices = new List<Vector3>();
    private List<int> triangles = new List<int>();
    private List<Vector2> uvs = new List<Vector2>();
    private List<Vector3> normals = new List<Vector3>();
    private bool meshDirty = true;
    #endregion

    #region Constants
    private const float MIN_SEGMENT_LENGTH = 0.001f;
    private const float MAX_DELTA_TIME = 0.016f;
    private const int MAX_WIRE_POINTS = 1000;
    #endregion

    [MenuItem("Tools/Wire Creator")]
    public static void ShowWindow()
    {
        var window = GetWindow<WireCreatorWindow>("Wire Creator");
        window.minSize = new Vector2(300, 600);
        window.LoadPresets();
    }

    private void OnEnable()
    {
        SceneView.duringSceneGui += OnSceneGUI;
        EditorApplication.update += Update;
        CreatePreviewObject();
        LoadPresets();
        
        // Initialize default curve if needed
        if (slackDistribution == null || slackDistribution.keys.Length == 0)
        {
            slackDistribution = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
        }
    }

    private void OnDisable()
    {
        SceneView.duringSceneGui -= OnSceneGUI;
        EditorApplication.update -= Update;
        DestroyPreviewObject();
        SavePresets();
    }

    private void CreatePreviewObject()
    {
        if (previewObject == null)
        {
            previewObject = new GameObject("Wire Preview");
            previewObject.hideFlags = HideFlags.HideAndDontSave;
            previewMeshFilter = previewObject.AddComponent<MeshFilter>();
            previewMeshRenderer = previewObject.AddComponent<MeshRenderer>();

            // Create default material if none assigned
            if (wireMaterial == null)
            {
                wireMaterial = new Material(Shader.Find("Standard"));
                wireMaterial.color = new Color(0.3f, 0.3f, 0.3f);
            }
            previewMeshRenderer.material = wireMaterial;
        }
    }

    private void DestroyPreviewObject()
    {
        if (previewObject != null)
        {
            DestroyImmediate(previewObject);
        }
    }

    private void OnGUI()
    {
        using (var scroll = new EditorGUILayout.ScrollViewScope(Vector2.zero))
        {
            DrawHeader();
            DrawPresetSection();
            DrawWireParameters();
            DrawPhysicsParameters();
            DrawAdvancedSettings();
            DrawSlackSettings();
            DrawControls();
            DrawInfo();
        }

        if (GUI.changed)
        {
            meshDirty = true;
            SceneView.RepaintAll();
        }
    }

    private void DrawHeader()
    {
        EditorGUILayout.LabelField("Wire Creator", EditorStyles.largeLabel);
        EditorGUILayout.Space();
    }

    private void DrawPresetSection()
    {
        showPresets = EditorGUILayout.Foldout(showPresets, "Presets", true);
        if (showPresets)
        {
            using (new EditorGUI.IndentLevelScope())
            {
                if (presets.Count > 0)
                {
                    string[] presetNames = presets.Select(p => p.name).ToArray();
                    int newIndex = EditorGUILayout.Popup("Load Preset", selectedPresetIndex, presetNames);
                    if (newIndex != selectedPresetIndex && newIndex >= 0)
                    {
                        selectedPresetIndex = newIndex;
                        LoadPreset(presets[selectedPresetIndex]);
                    }
                }

                using (new EditorGUILayout.HorizontalScope())
                {
                    newPresetName = EditorGUILayout.TextField("Preset Name", newPresetName);
                    if (GUILayout.Button("Save", GUILayout.Width(60)))
                    {
                        SaveCurrentAsPreset(newPresetName);
                    }
                }

                if (selectedPresetIndex >= 0 && selectedPresetIndex < presets.Count)
                {
                    if (GUILayout.Button("Delete Selected Preset"))
                    {
                        presets.RemoveAt(selectedPresetIndex);
                        selectedPresetIndex = -1;
                    }
                }
            }
        }
        EditorGUILayout.Space();
    }

    private void DrawWireParameters()
    {
        EditorGUILayout.LabelField("Wire Parameters", EditorStyles.boldLabel);
        using (new EditorGUI.IndentLevelScope())
        {
            wireRadius = EditorGUILayout.FloatField("Wire Radius", Mathf.Max(0.001f, wireRadius));
            wireResolution = EditorGUILayout.IntSlider("Resolution", wireResolution, 3, 32);
            wireMaterial = (Material)EditorGUILayout.ObjectField("Material", wireMaterial, typeof(Material), false);
        }
        EditorGUILayout.Space();
    }

    private void DrawPhysicsParameters()
    {
        EditorGUILayout.LabelField("Physics Parameters", EditorStyles.boldLabel);
        using (new EditorGUI.IndentLevelScope())
        {
            gravity = EditorGUILayout.FloatField("Gravity", gravity);
            stiffness = EditorGUILayout.Slider("Stiffness", stiffness, 0.1f, 1f);
            damping = EditorGUILayout.Slider("Damping", damping, 0.9f, 0.999f);
            simulationIterations = EditorGUILayout.IntSlider("Solver Iterations", simulationIterations, 1, 50);
            
            autoSubdivide = EditorGUILayout.Toggle("Auto Subdivide", autoSubdivide);
            if (autoSubdivide)
            {
                subdivisions = EditorGUILayout.IntSlider("Subdivisions", subdivisions, 1, 20);
            }
        }
        EditorGUILayout.Space();
    }

    private void DrawAdvancedSettings()
    {
        showAdvancedSettings = EditorGUILayout.Foldout(showAdvancedSettings, "Advanced Settings", true);
        if (showAdvancedSettings)
        {
            using (new EditorGUI.IndentLevelScope())
            {
                windStrength = EditorGUILayout.Slider("Wind Strength", windStrength, 0f, 10f);
                if (windStrength > 0)
                {
                    windDirection = EditorGUILayout.Vector3Field("Wind Direction", windDirection.normalized);
                }
                airResistance = EditorGUILayout.Slider("Air Resistance", airResistance, 0f, 0.1f);
            }
        }
        EditorGUILayout.Space();
    }

    private void DrawSlackSettings()
    {
        EditorGUILayout.LabelField("Wire Slack", EditorStyles.boldLabel);
        using (new EditorGUI.IndentLevelScope())
        {
            slackFactor = EditorGUILayout.Slider("Slack Factor", slackFactor, 1f, 5f);
            slackDistribution = EditorGUILayout.CurveField("Slack Distribution", slackDistribution);
            EditorGUILayout.HelpBox("Slack Factor adds extra length. Distribution curve controls where slack is applied (0=start, 1=end).", MessageType.Info);
        }
        EditorGUILayout.Space();
    }

    private void DrawControls()
    {
        EditorGUILayout.LabelField("Controls", EditorStyles.boldLabel);
        
        using (new EditorGUI.DisabledScope(wirePoints.Count >= MAX_WIRE_POINTS))
        {
            GUI.color = isPlacingPoints ? Color.green : Color.white;
            if (GUILayout.Button(isPlacingPoints ? "Stop Placing Points" : "Start Placing Points"))
            {
                TogglePointPlacement();
            }
            GUI.color = Color.white;
        }

        using (new EditorGUI.DisabledScope(originalPoints.Count < 2))
        {
            GUI.color = isSimulating ? Color.red : Color.white;
            if (GUILayout.Button(isSimulating ? "Stop Simulation" : "Start Simulation"))
            {
                ToggleSimulation();
            }
            GUI.color = Color.white;
        }

        using (new EditorGUILayout.HorizontalScope())
        {
            using (new EditorGUI.DisabledScope(originalPoints.Count == 0))
            {
                if (GUILayout.Button("Reset"))
                {
                    ResetToOriginalPoints();
                }
            }

            using (new EditorGUI.DisabledScope(!isSimulating))
            {
                if (GUILayout.Button("Apply Changes"))
                {
                    BuildSegments();
                }
            }
        }

        if (GUILayout.Button("Clear All"))
        {
            ClearAll();
        }

        EditorGUILayout.Space();

        using (new EditorGUI.DisabledScope(wirePoints.Count < 2))
        {
            if (GUILayout.Button("Create Wire Object", GUILayout.Height(30)))
            {
                CreateWireGameObject();
            }
        }
    }

    private void DrawInfo()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Information", EditorStyles.boldLabel);
        using (new EditorGUI.IndentLevelScope())
        {
            EditorGUILayout.LabelField($"Control Points: {originalPoints.Count}");
            EditorGUILayout.LabelField($"Simulation Points: {wirePoints.Count}");
            EditorGUILayout.LabelField($"Segments: {wireSegments.Count}");
            if (wirePoints.Count > 0)
            {
                float totalLength = CalculateTotalWireLength();
                EditorGUILayout.LabelField($"Wire Length: {totalLength:F2}m");
            }
        }
    }

    #region Preset Management
    private void LoadPresets()
    {
        string presetPath = "Assets/Editor/WireCreatorPresets.asset";
        var presetAsset = AssetDatabase.LoadAssetAtPath<ScriptableObject>(presetPath);
        // For now, create default presets
        if (presets.Count == 0)
        {
            CreateDefaultPresets();
        }
    }

    private void SavePresets()
    {
        // Save presets to EditorPrefs for persistence
        EditorPrefs.SetInt("WireCreator_PresetCount", presets.Count);
        for (int i = 0; i < presets.Count; i++)
        {
            string prefix = $"WireCreator_Preset_{i}_";
            EditorPrefs.SetString(prefix + "name", presets[i].name);
            EditorPrefs.SetFloat(prefix + "radius", presets[i].radius);
            EditorPrefs.SetInt(prefix + "resolution", presets[i].resolution);
            EditorPrefs.SetFloat(prefix + "gravity", presets[i].gravity);
            EditorPrefs.SetFloat(prefix + "stiffness", presets[i].stiffness);
            EditorPrefs.SetFloat(prefix + "damping", presets[i].damping);
            EditorPrefs.SetInt(prefix + "iterations", presets[i].iterations);
            EditorPrefs.SetFloat(prefix + "slackFactor", presets[i].slackFactor);
        }
    }

    private void CreateDefaultPresets()
    {
        presets.Add(new WirePreset { name = "Electrical Wire", radius = 0.02f, resolution = 6, stiffness = 0.9f, damping = 0.98f });
        presets.Add(new WirePreset { name = "Rope", radius = 0.05f, resolution = 8, stiffness = 0.7f, damping = 0.95f, slackFactor = 1.8f });
        presets.Add(new WirePreset { name = "Chain", radius = 0.03f, resolution = 12, stiffness = 0.95f, damping = 0.99f });
        presets.Add(new WirePreset { name = "Flexible Cable", radius = 0.04f, resolution = 10, stiffness = 0.6f, damping = 0.92f, slackFactor = 2.0f });
    }

    private void LoadPreset(WirePreset preset)
    {
        wireRadius = preset.radius;
        wireResolution = preset.resolution;
        gravity = preset.gravity;
        stiffness = preset.stiffness;
        damping = preset.damping;
        simulationIterations = preset.iterations;
        slackFactor = preset.slackFactor;
        wireMaterial = preset.material;
        meshDirty = true;
    }

    private void SaveCurrentAsPreset(string name)
    {
        var preset = new WirePreset
        {
            name = name,
            radius = wireRadius,
            resolution = wireResolution,
            gravity = gravity,
            stiffness = stiffness,
            damping = damping,
            iterations = simulationIterations,
            slackFactor = slackFactor,
            material = wireMaterial
        };
        presets.Add(preset);
        newPresetName = "New Preset";
    }
    #endregion

    #region Control Methods
    private void TogglePointPlacement()
    {
        isPlacingPoints = !isPlacingPoints;
        if (isPlacingPoints) 
        {
            isSimulating = false;
        }
    }

    private void ToggleSimulation()
    {
        isSimulating = !isSimulating;
        if (isSimulating)
        {
            isPlacingPoints = false;
            lastSimulationTime = EditorApplication.timeSinceStartup;
            if (autoSubdivide)
            {
                SubdivideWire();
            }
        }
    }

    private float CalculateTotalWireLength()
    {
        float length = 0f;
        for (int i = 0; i < wirePoints.Count - 1; i++)
        {
            length += Vector3.Distance(wirePoints[i].position, wirePoints[i + 1].position);
        }
        return length;
    }

    private void ClearAll()
    {
        wirePoints.Clear();
        originalPoints.Clear();
        wireSegments.Clear();
        isSimulating = false;
        meshDirty = true;
        UpdatePreviewMesh();
    }
    #endregion

    private void ResetToOriginalPoints()
    {
        wirePoints.Clear();
        wireSegments.Clear();

        for (int i = 0; i < originalPoints.Count; i++)
        {
            bool isFixed = (i == 0 || i == originalPoints.Count - 1);
            wirePoints.Add(new WirePoint(originalPoints[i], isFixed));
        }

        BuildSegments();
        isSimulating = false;
    }

    private void SubdivideWire()
    {
        if (originalPoints.Count < 2) return;

        List<WirePoint> newPoints = new List<WirePoint>();

        // Add subdivided points between each pair of original points
        for (int i = 0; i < originalPoints.Count - 1; i++)
        {
            Vector3 start = originalPoints[i];
            Vector3 end = originalPoints[i + 1];

            // Add start point (fixed if it's the first point)
            newPoints.Add(new WirePoint(start, i == 0));

            // Add subdivision points with slight offset to help initial simulation
            for (int j = 1; j < subdivisions; j++)
            {
                float t = (float)j / subdivisions;
                Vector3 pos = Vector3.Lerp(start, end, t);

                // Add slight downward offset to help wire settle
                if (!Mathf.Approximately(slackFactor, 1f))
                {
                    pos.y -= 0.01f * (slackFactor - 1f);
                }

                newPoints.Add(new WirePoint(pos, false));
            }
        }

        // Add last point (always fixed)
        newPoints.Add(new WirePoint(originalPoints[originalPoints.Count - 1], true));

        wirePoints = newPoints;
        BuildSegments();
    }

    private void BuildSegments()
    {
        wireSegments.Clear();

        // Calculate total wire length for slack distribution
        float totalLength = 0f;
        List<float> originalLengths = new List<float>();

        for (int i = 0; i < wirePoints.Count - 1; i++)
        {
            float distance = Vector3.Distance(wirePoints[i].position, wirePoints[i + 1].position);
            originalLengths.Add(distance);
            totalLength += distance;
        }

        // Apply slack based on position along wire
        float currentLength = 0f;
        for (int i = 0; i < wirePoints.Count - 1; i++)
        {
            float t = totalLength > 0 ? currentLength / totalLength : (float)i / (wirePoints.Count - 2);
            float localSlackFactor = Mathf.Lerp(1f, slackFactor, slackDistribution.Evaluate(t));

            float restLength = originalLengths[i] * localSlackFactor;
            wireSegments.Add(new WireSegment(i, i + 1, restLength));

            currentLength += originalLengths[i];
        }
    }

    private void OnSceneGUI(SceneView sceneView)
    {
        Event e = Event.current;

        // Handle point placement
        if (isPlacingPoints && e.type == EventType.MouseDown && e.button == 0)
        {
            Ray ray = HandleUtility.GUIPointToWorldRay(e.mousePosition);

            // Cast ray to find placement position
            RaycastHit hit;
            Vector3 position;

            if (Physics.Raycast(ray, out hit))
            {
                position = hit.point;
            }
            else
            {
                // Place at a default distance if no collision
                position = ray.origin + ray.direction * 10f;
            }

            // Add to original points
            originalPoints.Add(position);

            // Add to wire points
            bool isFixed = originalPoints.Count == 1; // Only first point is fixed by default
            wirePoints.Add(new WirePoint(position, isFixed));

            // Build segments
            BuildSegments();

            e.Use();
            UpdatePreviewMesh();
            Repaint();
        }

        // Draw original control points when not simulating
        if (!isSimulating && originalPoints.Count > 0)
        {
            Handles.color = Color.green;
            for (int i = 0; i < originalPoints.Count; i++)
            {
                float size = HandleUtility.GetHandleSize(originalPoints[i]) * 0.15f;
                Handles.SphereHandleCap(0, originalPoints[i], Quaternion.identity, size, EventType.Repaint);

                // Allow dragging of original points
                EditorGUI.BeginChangeCheck();
                Vector3 newPos = Handles.PositionHandle(originalPoints[i], Quaternion.identity);
                if (EditorGUI.EndChangeCheck())
                {
                    originalPoints[i] = newPos;
                    if (i < wirePoints.Count)
                    {
                        wirePoints[i].position = newPos;
                        wirePoints[i].oldPosition = newPos;
                    }
                    BuildSegments();
                    UpdatePreviewMesh();
                }
            }

            // Draw lines between original points
            Handles.color = Color.green * 0.5f;
            for (int i = 0; i < originalPoints.Count - 1; i++)
            {
                Handles.DrawLine(originalPoints[i], originalPoints[i + 1]);
            }
        }

        // Draw simulation points when simulating
        if (isSimulating && wirePoints.Count > 0)
        {
            for (int i = 0; i < wirePoints.Count; i++)
            {
                Handles.color = wirePoints[i].isFixed ? Color.red : Color.yellow;
                float size = HandleUtility.GetHandleSize(wirePoints[i].position) * 0.08f;
                Handles.SphereHandleCap(0, wirePoints[i].position, Quaternion.identity, size, EventType.Repaint);
            }

            // Draw wire segments
            Handles.color = Color.cyan;
            for (int i = 0; i < wirePoints.Count - 1; i++)
            {
                Handles.DrawLine(wirePoints[i].position, wirePoints[i + 1].position);
            }
        }
    }

    private void Update()
    {
        if (isSimulating && wirePoints.Count >= 2)
        {
            double currentTime = EditorApplication.timeSinceStartup;
            float deltaTime = (float)(currentTime - lastSimulationTime);
            lastSimulationTime = currentTime;

            // Limit delta time to prevent instability
            deltaTime = Mathf.Min(deltaTime, MAX_DELTA_TIME);

            SimulateWire(deltaTime);
            UpdatePreviewMesh();
            SceneView.RepaintAll();
        }
    }

    private void SimulateWire(float deltaTime)
    {
        // Apply forces and update positions using improved Verlet integration
        for (int i = 0; i < wirePoints.Count; i++)
        {
            if (!wirePoints[i].isFixed)
            {
                Vector3 currentPos = wirePoints[i].position;
                Vector3 velocity = (currentPos - wirePoints[i].oldPosition) * damping;
                
                // Apply gravity
                Vector3 acceleration = Vector3.up * gravity;
                
                // Apply wind force
                if (windStrength > 0)
                {
                    Vector3 windForce = windDirection.normalized * windStrength;
                    acceleration += windForce / wirePoints[i].mass;
                }
                
                // Apply air resistance
                if (airResistance > 0)
                {
                    Vector3 airResistanceForce = -velocity * airResistance;
                    acceleration += airResistanceForce / wirePoints[i].mass;
                }
                
                // Update position using Verlet integration
                wirePoints[i].oldPosition = currentPos;
                wirePoints[i].position = currentPos + velocity + acceleration * deltaTime * deltaTime;
                wirePoints[i].velocity = velocity;
            }
        }

        // Constraint iterations with improved stability
        for (int iteration = 0; iteration < simulationIterations; iteration++)
        {
            // Distance constraints
            foreach (var segment in wireSegments)
            {
                var pointA = wirePoints[segment.pointA];
                var pointB = wirePoints[segment.pointB];
                
                Vector3 delta = pointB.position - pointA.position;
                float distance = delta.magnitude;

                if (distance > MIN_SEGMENT_LENGTH)
                {
                    float error = (distance - segment.restLength) / distance;
                    Vector3 correction = delta * error * 0.5f * stiffness * segment.stiffness;

                    // Apply mass-based corrections
                    float totalMass = pointA.mass + pointB.mass;
                    float massRatioA = pointB.mass / totalMass;
                    float massRatioB = pointA.mass / totalMass;

                    if (!pointA.isFixed)
                        pointA.position += correction * massRatioA;
                    if (!pointB.isFixed)
                        pointB.position -= correction * massRatioB;
                }
            }
            
            // Additional stability: prevent points from moving too far in one frame
            for (int i = 0; i < wirePoints.Count; i++)
            {
                if (!wirePoints[i].isFixed)
                {
                    Vector3 movement = wirePoints[i].position - wirePoints[i].oldPosition;
                    float maxMovement = wireRadius * 2f; // Limit movement based on wire radius
                    if (movement.magnitude > maxMovement)
                    {
                        wirePoints[i].position = wirePoints[i].oldPosition + movement.normalized * maxMovement;
                    }
                }
            }
        }
        
        meshDirty = true;
    }

    private void UpdatePreviewMesh()
    {
        if (previewMeshFilter == null || wirePoints.Count < 2 || !meshDirty)
            return;

        GenerateWireMesh();

        if (vertices.Count > 0)
        {
            Mesh mesh = previewMeshFilter.mesh;
            if (mesh == null)
            {
                mesh = new Mesh();
                mesh.name = "Wire Preview Mesh";
            }
            else
            {
                mesh.Clear();
            }

            mesh.vertices = vertices.ToArray();
            mesh.triangles = triangles.ToArray();
            mesh.uv = uvs.ToArray();
            
            if (normals.Count == vertices.Count)
            {
                mesh.normals = normals.ToArray();
            }
            else
            {
                mesh.RecalculateNormals();
            }
            
            mesh.RecalculateBounds();
            previewMeshFilter.mesh = mesh;
        }
        
        meshDirty = false;
    }

    private void GenerateWireMesh()
    {
        vertices.Clear();
        triangles.Clear();
        uvs.Clear();
        normals.Clear();

        if (wirePoints.Count < 2) return;

        // Calculate total length for UV mapping
        float totalLength = CalculateTotalWireLength();
        List<float> segmentLengths = new List<float>();
        for (int i = 0; i < wirePoints.Count - 1; i++)
        {
            float length = Vector3.Distance(wirePoints[i].position, wirePoints[i + 1].position);
            segmentLengths.Add(length);
        }

        float currentDistance = 0f;

        // Generate vertices for each point with improved normal calculation
        for (int i = 0; i < wirePoints.Count; i++)
        {
            Vector3 forward = CalculateForwardDirection(i);
            Vector3 right = CalculateRightDirection(forward);
            Vector3 up = Vector3.Cross(forward, right).normalized;

            // Adaptive radius based on wire tension (optional enhancement)
            float adaptiveRadius = wireRadius;
            if (i > 0 && i < wirePoints.Count - 1)
            {
                float tension = CalculateLocalTension(i);
                adaptiveRadius *= Mathf.Lerp(0.8f, 1.2f, tension);
            }

            // Create ring of vertices with better distribution
            for (int j = 0; j < wireResolution; j++)
            {
                float angle = (float)j / wireResolution * Mathf.PI * 2f;
                Vector3 radialOffset = (right * Mathf.Cos(angle) + up * Mathf.Sin(angle)) * adaptiveRadius;
                Vector3 vertexPos = wirePoints[i].position + radialOffset;
                
                vertices.Add(vertexPos);
                normals.Add(radialOffset.normalized);

                // Improved UV mapping
                float u = (float)j / wireResolution;
                float v = totalLength > 0 ? currentDistance / totalLength : (float)i / (wirePoints.Count - 1);
                uvs.Add(new Vector2(u, v));
            }

            if (i < segmentLengths.Count)
                currentDistance += segmentLengths[i];
        }

        // Generate triangles with proper winding order
        for (int i = 0; i < wirePoints.Count - 1; i++)
        {
            for (int j = 0; j < wireResolution; j++)
            {
                int current = i * wireResolution + j;
                int next = i * wireResolution + (j + 1) % wireResolution;
                int currentNext = (i + 1) * wireResolution + j;
                int nextNext = (i + 1) * wireResolution + (j + 1) % wireResolution;

                // Ensure proper winding order for correct normals
                triangles.Add(current);
                triangles.Add(next);
                triangles.Add(currentNext);

                triangles.Add(next);
                triangles.Add(nextNext);
                triangles.Add(currentNext);
            }
        }
    }

    private Vector3 CalculateForwardDirection(int index)
    {
        if (wirePoints.Count < 2) return Vector3.forward;

        if (index == 0)
        {
            return (wirePoints[1].position - wirePoints[0].position).normalized;
        }
        else if (index == wirePoints.Count - 1)
        {
            return (wirePoints[index].position - wirePoints[index - 1].position).normalized;
        }
        else
        {
            Vector3 dir1 = (wirePoints[index].position - wirePoints[index - 1].position).normalized;
            Vector3 dir2 = (wirePoints[index + 1].position - wirePoints[index].position).normalized;
            Vector3 avgDir = (dir1 + dir2).normalized;
            
            return avgDir.magnitude > 0.001f ? avgDir : dir2;
        }
    }

    private Vector3 CalculateRightDirection(Vector3 forward)
    {
        Vector3 right = Vector3.Cross(Vector3.up, forward).normalized;
        if (right.magnitude < 0.001f)
        {
            right = Vector3.Cross(Vector3.right, forward).normalized;
        }
        return right;
    }

    private float CalculateLocalTension(int index)
    {
        if (index <= 0 || index >= wirePoints.Count - 1) return 0.5f;
        
        float prevDist = Vector3.Distance(wirePoints[index - 1].position, wirePoints[index].position);
        float nextDist = Vector3.Distance(wirePoints[index].position, wirePoints[index + 1].position);
        float avgSegmentLength = (prevDist + nextDist) * 0.5f;
        
        // Calculate how much this segment is stretched compared to rest length
        if (index - 1 < wireSegments.Count)
        {
            float restLength = wireSegments[index - 1].restLength;
            return Mathf.Clamp01(avgSegmentLength / restLength);
        }
        
        return 0.5f;
    }

    private void CreateWireGameObject()
    {
        // Generate final mesh
        GenerateWireMesh();

        if (vertices.Count == 0)
        {
            Debug.LogWarning("Cannot create wire object: No mesh data generated");
            return;
        }

        // Create new game object with proper naming
        string wireName = $"Wire_{System.DateTime.Now:HHmmss}";
        GameObject wireObject = new GameObject(wireName);
        
        // Add components
        MeshFilter meshFilter = wireObject.AddComponent<MeshFilter>();
        MeshRenderer meshRenderer = wireObject.AddComponent<MeshRenderer>();
        MeshCollider meshCollider = wireObject.AddComponent<MeshCollider>();

        // Create and assign mesh
        Mesh mesh = new Mesh();
        mesh.name = $"{wireName}_Mesh";
        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.uv = uvs.ToArray();
        
        if (normals.Count == vertices.Count)
        {
            mesh.normals = normals.ToArray();
        }
        else
        {
            mesh.RecalculateNormals();
        }
        
        mesh.RecalculateBounds();
        mesh.Optimize();

        // Assign mesh to components
        meshFilter.mesh = mesh;
        meshCollider.sharedMesh = mesh;
        meshCollider.convex = false; // For better collision detection with thin wires

        // Assign material
        Material finalMaterial = wireMaterial;
        if (finalMaterial == null)
        {
            finalMaterial = new Material(Shader.Find("Standard"));
            finalMaterial.color = new Color(0.3f, 0.3f, 0.3f);
            finalMaterial.SetFloat("_Metallic", 0.8f);
            finalMaterial.SetFloat("_Glossiness", 0.6f);
        }
        meshRenderer.material = finalMaterial;

        // Add wire component for runtime behavior (optional)
        var wireComponent = wireObject.AddComponent<WireComponent>();
        wireComponent.Initialize(wirePoints.ToArray(), wireSegments.ToArray());

        // Position at scene center if no points exist
        if (originalPoints.Count > 0)
        {
            Vector3 center = Vector3.zero;
            foreach (var point in originalPoints)
            {
                center += point;
            }
            center /= originalPoints.Count;
            wireObject.transform.position = Vector3.zero; // Keep at origin, mesh already positioned
        }

        // Create asset for the mesh
        string assetPath = $"Assets/Generated/Meshes/{wireName}_Mesh.asset";
        System.IO.Directory.CreateDirectory("Assets/Generated/Meshes");
        AssetDatabase.CreateAsset(mesh, assetPath);
        AssetDatabase.SaveAssets();

        // Select and focus the created object
        Selection.activeGameObject = wireObject;
        EditorGUIUtility.PingObject(wireObject);

        // Log success with detailed info
        float wireLength = CalculateTotalWireLength();
        Debug.Log($"Created wire object '{wireName}' with {wirePoints.Count} points, {wireSegments.Count} segments, length: {wireLength:F2}m");

        // Optionally keep the preview for creating more wires
        if (!Event.current.shift)
        {
            ClearAll();
        }
    }

    // Runtime component for created wires
    [System.Serializable]
    public class WireComponent : MonoBehaviour
    {
        [SerializeField] private WirePoint[] runtimePoints;
        [SerializeField] private WireSegment[] runtimeSegments;
        
        public void Initialize(WirePoint[] points, WireSegment[] segments)
        {
            runtimePoints = points;
            runtimeSegments = segments;
        }
        
        // Add runtime wire behavior here if needed
        // For example: wind effects, collision response, etc.
    }
}
#endif
